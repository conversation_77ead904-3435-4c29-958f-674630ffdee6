// Global variables
let apiBaseUrl = 'http://localhost:8000';

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    updateConnectionStatus(false);
    testConnection();
});

// Update API base URL when input changes
document.getElementById('apiUrl').addEventListener('input', function() {
    apiBaseUrl = this.value.trim();
});

// Test API connection
async function testConnection() {
    const statusIndicator = document.getElementById('connectionStatus');
    const resultContainer = document.getElementById('connectionResult');
    const resultContent = document.getElementById('connectionContent');
    
    statusIndicator.className = 'status-indicator';
    resultContainer.style.display = 'block';
    resultContent.innerHTML = '<div class="loading">Testing connection...</div>';
    
    try {
        const response = await fetch(`${apiBaseUrl}/health`);
        const data = await response.json();
        
        if (response.ok) {
            updateConnectionStatus(true);
            resultContent.innerHTML = `<div class="success">✅ Connected successfully!<br><br>Response: ${JSON.stringify(data, null, 2)}</div>`;
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        updateConnectionStatus(false);
        resultContent.innerHTML = `<div class="error">❌ Connection failed!<br><br>Error: ${error.message}<br><br>Make sure your API is running at: ${apiBaseUrl}</div>`;
    }
}

// Update connection status indicator
function updateConnectionStatus(isOnline) {
    const statusIndicator = document.getElementById('connectionStatus');
    statusIndicator.className = `status-indicator ${isOnline ? 'status-online' : 'status-offline'}`;
}

// Preview uploaded image
function previewImage(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);
    const file = input.files[0];
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
        
        // Clear URL input if file is selected
        const urlInputId = inputId.replace('File', 'Url');
        document.getElementById(urlInputId).value = '';
    }
}

// Preview image from URL
function previewImageUrl(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);
    const url = input.value.trim();
    
    if (url) {
        preview.src = url;
        preview.style.display = 'block';
        preview.onerror = function() {
            preview.style.display = 'none';
            alert('Failed to load image from URL. Please check the URL and try again.');
        };
        
        // Clear file input if URL is entered
        const fileInputId = inputId.replace('Url', 'File');
        document.getElementById(fileInputId).value = '';
    }
}

// Test ANPR functionality
async function testANPR() {
    const fileInput = document.getElementById('anprFile');
    const urlInput = document.getElementById('anprUrl');
    const resultDiv = document.getElementById('anprResult');
    const button = document.getElementById('anprBtn');
    
    // Check if we have an image
    const hasFile = fileInput.files.length > 0;
    const hasUrl = urlInput.value.trim() !== '';
    
    if (!hasFile && !hasUrl) {
        resultDiv.innerHTML = '<div class="error">❌ Please select an image file or enter an image URL.</div>';
        return;
    }
    
    // Disable button and show loading
    button.disabled = true;
    button.textContent = '🔄 Analyzing...';
    resultDiv.innerHTML = '<div class="loading">Analyzing license plate... This may take a moment for the first request as models are loading.</div>';
    
    try {
        let response;
        
        if (hasFile) {
            // Upload file
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            response = await fetch(`${apiBaseUrl}/anpr/analyze`, {
                method: 'POST',
                body: formData
            });
        } else {
            // Use URL
            const formData = new FormData();
            
            // Download image from URL and create a blob
            const imageResponse = await fetch(urlInput.value);
            if (!imageResponse.ok) {
                throw new Error('Failed to fetch image from URL');
            }
            const imageBlob = await imageResponse.blob();
            formData.append('file', imageBlob, 'image.jpg');
            
            response = await fetch(`${apiBaseUrl}/anpr/analyze`, {
                method: 'POST',
                body: formData
            });
        }
        
        const data = await response.json();
        
        if (response.ok) {
            resultDiv.innerHTML = `<div class="success">✅ Analysis completed successfully!</div><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
        } else {
            throw new Error(`HTTP ${response.status}: ${data.detail || response.statusText}`);
        }
        
    } catch (error) {
        resultDiv.innerHTML = `<div class="error">❌ Analysis failed!<br><br>Error: ${error.message}</div>`;
    } finally {
        // Re-enable button
        button.disabled = false;
        button.textContent = '🔍 Analyze License Plate';
    }
}

// Test Dispenser functionality
async function testDispenser() {
    const fileInput = document.getElementById('dispenserFile');
    const urlInput = document.getElementById('dispenserUrl');
    const resultDiv = document.getElementById('dispenserResult');
    const button = document.getElementById('dispenserBtn');
    
    // Check if we have an image
    const hasFile = fileInput.files.length > 0;
    const hasUrl = urlInput.value.trim() !== '';
    
    if (!hasFile && !hasUrl) {
        resultDiv.innerHTML = '<div class="error">❌ Please select an image file or enter an image URL.</div>';
        return;
    }
    
    // Disable button and show loading
    button.disabled = true;
    button.textContent = '🔄 Reading...';
    resultDiv.innerHTML = '<div class="loading">Reading dispenser... This may take a moment for the first request as models are loading.</div>';
    
    try {
        let response;
        
        if (hasFile) {
            // Upload file
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            
            response = await fetch(`${apiBaseUrl}/dispenser/analyze`, {
                method: 'POST',
                body: formData
            });
        } else {
            // Use URL
            const formData = new FormData();
            
            // Download image from URL and create a blob
            const imageResponse = await fetch(urlInput.value);
            if (!imageResponse.ok) {
                throw new Error('Failed to fetch image from URL');
            }
            const imageBlob = await imageResponse.blob();
            formData.append('file', imageBlob, 'image.jpg');
            
            response = await fetch(`${apiBaseUrl}/dispenser/analyze`, {
                method: 'POST',
                body: formData
            });
        }
        
        const data = await response.json();
        
        if (response.ok) {
            resultDiv.innerHTML = `<div class="success">✅ Reading completed successfully!</div><br><pre>${JSON.stringify(data, null, 2)}</pre>`;
        } else {
            throw new Error(`HTTP ${response.status}: ${data.detail || response.statusText}`);
        }
        
    } catch (error) {
        resultDiv.innerHTML = `<div class="error">❌ Reading failed!<br><br>Error: ${error.message}</div>`;
    } finally {
        // Re-enable button
        button.disabled = false;
        button.textContent = '📊 Read Dispenser';
    }
}

// Utility function to format JSON nicely
function formatJSON(obj) {
    return JSON.stringify(obj, null, 2);
}

// Handle CORS errors gracefully
window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.message && event.reason.message.includes('CORS')) {
        console.warn('CORS error detected. Make sure your API allows cross-origin requests.');
    }
});
