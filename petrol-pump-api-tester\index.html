<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Petrol Pump AI API Tester</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .api-config {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #4CAF50;
        }

        .api-config h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .test-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .test-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .test-section h3 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5em;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .result-container {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
            min-height: 100px;
        }

        .result-container h4 {
            color: #333;
            margin-bottom: 15px;
        }

        .result-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
        }

        .success {
            color: #155724;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
        }

        .image-preview {
            max-width: 100%;
            max-height: 200px;
            margin: 10px 0;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #4CAF50;
        }

        .status-offline {
            background: #f44336;
        }

        @media (max-width: 768px) {
            .test-sections {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 Petrol Pump AI API Tester</h1>
            <p>Test your ANPR and Dispenser Reading Models</p>
        </div>

        <div class="main-content">
            <!-- API Configuration Section -->
            <div class="api-config">
                <h3>🔧 API Configuration</h3>
                <div class="input-group">
                    <label for="apiUrl">API Base URL:</label>
                    <input type="text" id="apiUrl" value="http://localhost:8000" placeholder="http://localhost:8000">
                </div>
                <button class="btn" onclick="testConnection()">
                    <span class="status-indicator" id="connectionStatus"></span>
                    Test Connection
                </button>
                <div id="connectionResult" class="result-container" style="display: none;">
                    <h4>Connection Status:</h4>
                    <div id="connectionContent" class="result-content"></div>
                </div>
            </div>

            <!-- Test Sections -->
            <div class="test-sections">
                <!-- ANPR Testing Section -->
                <div class="test-section">
                    <h3>🚙 License Plate Recognition (ANPR)</h3>
                    
                    <div class="input-group">
                        <label for="anprFile">Upload Image File:</label>
                        <input type="file" id="anprFile" accept="image/*" onchange="previewImage('anprFile', 'anprPreview')">
                    </div>
                    
                    <div class="input-group">
                        <label for="anprUrl">Or Image URL:</label>
                        <input type="url" id="anprUrl" placeholder="https://example.com/license-plate.jpg" onchange="previewImageUrl('anprUrl', 'anprPreview')">
                    </div>
                    
                    <img id="anprPreview" class="image-preview" style="display: none;" alt="Preview">
                    
                    <button class="btn" onclick="testANPR()" id="anprBtn">
                        🔍 Analyze License Plate
                    </button>
                    
                    <div class="result-container">
                        <h4>ANPR Results:</h4>
                        <div id="anprResult" class="result-content">No results yet. Upload an image and click "Analyze License Plate".</div>
                    </div>
                </div>

                <!-- Dispenser Testing Section -->
                <div class="test-section">
                    <h3>⛽ Dispenser Reading</h3>
                    
                    <div class="input-group">
                        <label for="dispenserFile">Upload Image File:</label>
                        <input type="file" id="dispenserFile" accept="image/*" onchange="previewImage('dispenserFile', 'dispenserPreview')">
                    </div>
                    
                    <div class="input-group">
                        <label for="dispenserUrl">Or Image URL:</label>
                        <input type="url" id="dispenserUrl" placeholder="https://example.com/dispenser.jpg" onchange="previewImageUrl('dispenserUrl', 'dispenserPreview')">
                    </div>
                    
                    <img id="dispenserPreview" class="image-preview" style="display: none;" alt="Preview">
                    
                    <button class="btn" onclick="testDispenser()" id="dispenserBtn">
                        📊 Read Dispenser
                    </button>
                    
                    <div class="result-container">
                        <h4>Dispenser Results:</h4>
                        <div id="dispenserResult" class="result-content">No results yet. Upload an image and click "Read Dispenser".</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
