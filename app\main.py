# app/main.py

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import shutil
import os
import requests
import tempfile
from PIL import Image
import io
from app.dispenser_reader import analyze_dispenser_display, get_price, set_price, load_dispenser_models
from app.anpr_reader import detect_and_read_plate, load_models as load_anpr_models
import cv2
import numpy as np
from typing import Optional

app = FastAPI(title="Petrol Pump AI API", description="AI models for dispenser reading and ANPR", version="1.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Global flags to track model loading
anpr_models_loaded = False
dispenser_models_loaded = False

@app.on_event("startup")
async def startup_event():
    """API startup - models will load on first request for memory efficiency"""
    print("🚀 Starting Petrol Pump AI API...")
    print("📦 Models will load on first request for optimal memory usage...")
    print("🎉 API ready! Models will load when needed.")

UPLOAD_DIR = "temp_uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

def download_image_from_url(url: str) -> str:
    """Download image from URL and save to temp file"""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        # Create temp file with proper extension
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            tmp_file.write(response.content)
            return tmp_file.name
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to download image from URL: {str(e)}")

def process_image_file(file_content: bytes, filename: str) -> str:
    """Process image file content and save to temp file with format conversion"""
    try:
        # Open image with PIL to handle all formats
        image = Image.open(io.BytesIO(file_content))

        # Convert to RGB if necessary (handles RGBA, P mode, etc.)
        if image.mode in ('RGBA', 'LA', 'P'):
            # Create white background for transparent images
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')

        # Save as JPEG for OpenCV compatibility
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            image.save(tmp_file.name, 'JPEG', quality=95)
            return tmp_file.name

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid image file: {str(e)}")

def cleanup_temp_file(filepath: str):
    """Clean up temporary file"""
    try:
        if os.path.exists(filepath):
            os.unlink(filepath)
    except:
        pass

@app.post("/dispenser")
async def read_dispenser(
    file: Optional[UploadFile] = File(None),
    image_url: Optional[str] = Form(None)
):
    """Read dispenser display from image file or URL"""

    if not file and not image_url:
        raise HTTPException(status_code=400, detail="Either file or image_url must be provided")

    filepath = None
    try:
        # Handle image input
        if file:
            file_content = await file.read()
            filepath = process_image_file(file_content, file.filename)
        else:
            filepath = download_image_from_url(image_url)

        # Analyze dispenser display using optimized function
        result = analyze_dispenser_display(filepath)

        return {
            "success": result["success"],
            "amount": result["amount"],
            "amount_numeric": float(result["amount"]) if result["amount"] != "Not Detected" else None,
            "price_per_litre": result["price_per_litre"],
            "calculated_litre": round(result["litres"], 2) if result["litres"] else None,
            "message": "Dispenser analysis completed successfully" if result["success"] else "Could not detect amount"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing dispenser image: {str(e)}")
    finally:
        # Clean up temp file
        if filepath:
            cleanup_temp_file(filepath)


@app.post("/anpr")
async def read_license_plate(
    file: Optional[UploadFile] = File(None),
    image_url: Optional[str] = Form(None)
):
    """Read license plate from image file or URL"""

    if not file and not image_url:
        raise HTTPException(status_code=400, detail="Either file or image_url must be provided")

    filepath = None
    try:
        # Handle image input
        if file:
            file_content = await file.read()
            filepath = process_image_file(file_content, file.filename)
        else:
            filepath = download_image_from_url(image_url)

        # Perform ANPR
        plate_text = detect_and_read_plate(filepath)

        return {
            "success": True,
            "plate_text": plate_text,
            "message": "License plate analysis completed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing license plate image: {str(e)}")
    finally:
        # Clean up temp file
        if filepath:
            cleanup_temp_file(filepath)


@app.get("/")
def root():
    return {"message": "Welcome to the Petrol Pump AI API"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "Petrol Pump AI API"}

@app.post("/set-price")
async def set_daily_price(price: float):
    """Set today's fuel price per litre"""
    if price <= 0:
        raise HTTPException(status_code=400, detail="Price must be greater than 0")

    success = set_price(price)
    if success:
        return {
            "success": True,
            "price_per_litre": price,
            "message": f"Daily price set to ₹{price:.2f} per litre"
        }
    else:
        raise HTTPException(status_code=500, detail="Failed to set price")

@app.get("/get-price")
async def get_daily_price():
    """Get current fuel price per litre"""
    price = get_price()
    return {
        "price_per_litre": price,
        "message": f"Current price: ₹{price:.2f} per litre"
    }