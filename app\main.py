# app/main.py

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.middleware.cors import CORSMiddleware
import shutil
import os
import requests
import tempfile
from PIL import Image
import io
from app.dispenser_reader import perform_full_ocr_analysis, get_or_set_price_per_litre, load_dispenser_models
from app.anpr_reader import detect_and_read_plate, load_models as load_anpr_models
import cv2
import numpy as np
from typing import Optional

app = FastAPI(title="Petrol Pump AI API", description="AI models for dispenser reading and ANPR", version="1.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Global flag to track model loading
models_loaded = False

@app.on_event("startup")
async def startup_event():
    """Load all models on startup for fast responses"""
    global models_loaded
    print("🚀 Starting Petrol Pump AI API...")
    print("📦 Loading all models on startup for optimal performance...")

    try:
        # Load ANPR models
        print("🔍 Loading ANPR models...")
        load_anpr_models()

        # Load Dispenser models
        print("⛽ Loading Dispenser models...")
        load_dispenser_models()

        models_loaded = True
        print("✅ All models loaded successfully! API ready for fast responses.")

    except Exception as e:
        print(f"❌ Error loading models: {e}")
        models_loaded = False

UPLOAD_DIR = "temp_uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

def download_image_from_url(url: str) -> str:
    """Download image from URL and save to temp file"""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        # Create temp file with proper extension
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            tmp_file.write(response.content)
            return tmp_file.name
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Failed to download image from URL: {str(e)}")

def process_image_file(file_content: bytes, filename: str) -> str:
    """Process image file content and save to temp file with format conversion"""
    try:
        # Open image with PIL to handle all formats
        image = Image.open(io.BytesIO(file_content))

        # Convert to RGB if necessary (handles RGBA, P mode, etc.)
        if image.mode in ('RGBA', 'LA', 'P'):
            # Create white background for transparent images
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')

        # Save as JPEG for OpenCV compatibility
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            image.save(tmp_file.name, 'JPEG', quality=95)
            return tmp_file.name

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid image file: {str(e)}")

def cleanup_temp_file(filepath: str):
    """Clean up temporary file"""
    try:
        if os.path.exists(filepath):
            os.unlink(filepath)
    except:
        pass

@app.post("/dispenser")
async def read_dispenser(
    file: Optional[UploadFile] = File(None),
    image_url: Optional[str] = Form(None)
):
    """Read dispenser display from image file or URL"""
    if not models_loaded:
        raise HTTPException(status_code=503, detail="Models are still loading. Please try again in a moment.")

    if not file and not image_url:
        raise HTTPException(status_code=400, detail="Either file or image_url must be provided")

    filepath = None
    try:
        # Handle image input
        if file:
            file_content = await file.read()
            filepath = process_image_file(file_content, file.filename)
        else:
            filepath = download_image_from_url(image_url)

        # Read image with OpenCV
        image = cv2.imread(filepath)
        if image is None:
            raise HTTPException(status_code=400, detail="Could not read image file")

        # Perform OCR analysis
        amount = perform_full_ocr_analysis(image, 'amount')

        # For demo purposes, use a fixed price. In production, this would come from your system
        price = 105.50  # Fixed price per litre

        try:
            amount_val = float(amount)
            litre_val = round(amount_val / price, 2) if price > 0 else None
        except:
            amount_val = None
            litre_val = None

        return {
            "success": True,
            "amount": amount,
            "amount_numeric": amount_val,
            "price_per_litre": price,
            "calculated_litre": litre_val,
            "message": "Dispenser reading completed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing dispenser image: {str(e)}")
    finally:
        # Clean up temp file
        if filepath:
            cleanup_temp_file(filepath)


@app.post("/anpr")
async def read_license_plate(
    file: Optional[UploadFile] = File(None),
    image_url: Optional[str] = Form(None)
):
    """Read license plate from image file or URL"""
    if not models_loaded:
        raise HTTPException(status_code=503, detail="Models are still loading. Please try again in a moment.")

    if not file and not image_url:
        raise HTTPException(status_code=400, detail="Either file or image_url must be provided")

    filepath = None
    try:
        # Handle image input
        if file:
            file_content = await file.read()
            filepath = process_image_file(file_content, file.filename)
        else:
            filepath = download_image_from_url(image_url)

        # Perform ANPR
        plate_text = detect_and_read_plate(filepath)

        return {
            "success": True,
            "plate_text": plate_text,
            "message": "License plate analysis completed successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing license plate image: {str(e)}")
    finally:
        # Clean up temp file
        if filepath:
            cleanup_temp_file(filepath)


@app.get("/")
def root():
    return {"message": "Welcome to the Petrol Pump AI API"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "Petrol Pump AI API"}