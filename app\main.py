# app/main.py

from fastapi import FastAPI, UploadFile, File
from fastapi.responses import JSONResponse
import shutil
import os
from app.dispenser_reader import perform_full_ocr_analysis, get_or_set_price_per_litre
from app.anpr_reader import detect_and_read_plate
import cv2

app = FastAPI(title="Petrol Pump AI API", description="AI models for dispenser reading and ANPR", version="1.0")

UPLOAD_DIR = "temp_uploads"
os.makedirs(UPLOAD_DIR, exist_ok=True)

@app.post("/dispenser")
async def read_dispenser(file: UploadFile = File(...)):
    try:
        filepath = os.path.join(UPLOAD_DIR, file.filename)
        with open(filepath, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        image = cv2.imread(filepath)
        if image is None:
            return JSONResponse(content={"error": "Invalid image file."}, status_code=400)

        amount = perform_full_ocr_analysis(image, 'amount')
        price = get_or_set_price_per_litre()

        try:
            amount_val = float(amount)
            litre_val = round(amount_val / price, 2) if price > 0 else None
        except:
            litre_val = None

        return {
            "amount": amount,
            "price_per_litre": price,
            "calculated_litre": litre_val
        }
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.post("/anpr")
async def read_license_plate(file: UploadFile = File(...)):
    try:
        filepath = os.path.join(UPLOAD_DIR, file.filename)
        with open(filepath, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        plate_text = detect_and_read_plate(filepath)
        return {"plate_text": plate_text}

    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)


@app.get("/")
def root():
    return {"message": "Welcome to the Petrol Pump AI API"}