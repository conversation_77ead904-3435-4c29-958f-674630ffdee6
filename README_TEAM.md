# Petrol Pump AI API - Team Guide

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- All dependencies installed (`pip install -r requirements.txt`)
- Local TrOCR models in `models/models/trocr/`

### Starting the API
```bash
# Navigate to project directory
cd petrol_pump_ai_api

# Start the API server
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# API will be available at: http://localhost:8000
```

## 📋 API Endpoints

### 1. Health Check
- **GET** `/health`
- **Purpose**: Check if API is running
- **Response**: `{"status": "healthy", "service": "Petrol Pump AI API"}`

### 2. Set Daily Price
- **POST** `/set-price?price=105.50`
- **Purpose**: Set today's fuel price per litre
- **Parameters**: `price` (float) - Price per litre
- **Response**: Success confirmation with set price

### 3. Get Current Price
- **GET** `/get-price`
- **Purpose**: Get current fuel price per litre
- **Response**: Current price information

### 4. Dispenser Analysis
- **POST** `/dispenser`
- **Purpose**: Analyze fuel dispenser display to extract amount and calculate litres
- **Input**: 
  - File upload: `file` (image file)
  - OR URL: `image_url` (image URL)
- **Response**:
```json
{
  "success": true,
  "amount": "1250.75",
  "amount_numeric": 1250.75,
  "price_per_litre": 105.50,
  "calculated_litre": 11.85,
  "message": "Dispenser analysis completed successfully"
}
```

### 5. ANPR (License Plate Recognition)
- **POST** `/anpr`
- **Purpose**: Extract license plate text from images
- **Input**: 
  - File upload: `file` (image file)
  - OR URL: `image_url` (image URL)
- **Response**:
```json
{
  "success": true,
  "plate_text": "ABC1234",
  "message": "License plate analysis completed successfully"
}
```

## 🧪 Testing with Postman

### Import Collection
1. Open Postman
2. Click **Import**
3. Select `Petrol_Pump_AI_API_Updated.postman_collection.json`
4. Collection will be imported with all endpoints

### Environment Setup
- Base URL is set to: `http://localhost:8000`
- Change this if running on different host/port

### Test Sequence
1. **Health Check** - Verify API is running
2. **Set Daily Price** - Set today's fuel price (e.g., 105.50)
3. **Get Current Price** - Verify price was set
4. **Dispenser Analysis** - Upload dispenser image
5. **ANPR Analysis** - Upload license plate image

## 🔧 Key Features

### Optimized Performance
- **Local Models**: TrOCR models loaded from local directory for fast startup
- **YOLO Detection**: Accurate region detection for both dispensers and license plates
- **Smart Processing**: Only TrOCR used for best accuracy (removed slower methods)

### Daily Price Management
- **Staff Control**: Set daily fuel price at start of day
- **Automatic Calculation**: Litres calculated automatically from amount and price
- **Persistent Storage**: Price stored in local files with date tracking

### Production Ready
- **Error Handling**: Comprehensive error responses
- **File Cleanup**: Automatic temporary file cleanup
- **CORS Enabled**: Ready for web frontend integration
- **Validation**: Input validation and type checking

## 📁 File Structure
```
petrol_pump_ai_api/
├── app/
│   ├── main.py                 # FastAPI application
│   ├── dispenser_reader.py     # Optimized dispenser analysis
│   └── anpr_reader.py          # License plate recognition
├── models/
│   ├── best.pt                 # YOLO model for dispensers
│   ├── license_plate_detector.pt # YOLO model for license plates
│   └── models/trocr/           # Local TrOCR model files
├── requirements.txt            # Python dependencies
├── test_api.py                # Test script
└── README_TEAM.md             # This file
```

## 🐛 Troubleshooting

### Common Issues
1. **Models not loading**: Ensure `models/models/trocr/` contains all TrOCR files
2. **YOLO errors**: Check that `.pt` files are in `models/` directory
3. **Port conflicts**: Change port in startup command if 8000 is busy
4. **Memory issues**: Ensure sufficient RAM (4GB+ recommended)

### Error Responses
- **503**: Models still loading, wait and retry
- **400**: Invalid input (missing file/URL, invalid price)
- **500**: Server error (check logs)

## 📞 Support
- Check console logs for detailed error messages
- Test with `python test_api.py` for basic functionality
- Verify model files are present and accessible

## 🎯 Usage Tips
1. **Set price daily**: Use `/set-price` endpoint each morning
2. **Image quality**: Better images = better results
3. **File formats**: Supports JPG, PNG, common image formats
4. **Batch processing**: API handles one image at a time efficiently
