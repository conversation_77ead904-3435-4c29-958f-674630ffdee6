version: '3.8'

services:
  petrol-pump-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: petrol-pump-ai-api
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      # Mount uploads directory for persistent storage
      - ./uploads:/app/uploads
      # Mount price files for persistence across restarts
      - ./price_data:/app/price_data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    networks:
      - petrol-pump-network

networks:
  petrol-pump-network:
    driver: bridge

volumes:
  uploads:
  price_data:
