#!/usr/bin/env python3
"""
Test script for Petrol Pump AI API
Run this to test the API locally before sharing with your team
"""

import requests
import json

# API base URL
BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing Health Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_set_price():
    """Test setting daily price"""
    print("\n💰 Testing Set Price Endpoint...")
    try:
        response = requests.post(f"{BASE_URL}/set-price", params={"price": 105.50})
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Set price failed: {e}")
        return False

def test_get_price():
    """Test getting current price"""
    print("\n📊 Testing Get Price Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/get-price")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Get price failed: {e}")
        return False

def test_dispenser_with_file():
    """Test dispenser endpoint with file upload"""
    print("\n⛽ Testing Dispenser Endpoint (File Upload)...")
    try:
        # You'll need to provide a test image file
        test_image = "test_dispenser.jpg"  # Update this path
        
        with open(test_image, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{BASE_URL}/dispenser", files=files)
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except FileNotFoundError:
        print("⚠️ Test image not found. Skipping file upload test.")
        return True
    except Exception as e:
        print(f"❌ Dispenser test failed: {e}")
        return False

def test_anpr_with_file():
    """Test ANPR endpoint with file upload"""
    print("\n🚗 Testing ANPR Endpoint (File Upload)...")
    try:
        # You'll need to provide a test image file
        test_image = "test_license_plate.jpg"  # Update this path
        
        with open(test_image, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{BASE_URL}/anpr", files=files)
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except FileNotFoundError:
        print("⚠️ Test image not found. Skipping file upload test.")
        return True
    except Exception as e:
        print(f"❌ ANPR test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting API Tests...")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_health),
        ("Set Price", test_set_price),
        ("Get Price", test_get_price),
        ("Dispenser Analysis", test_dispenser_with_file),
        ("ANPR Analysis", test_anpr_with_file),
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    passed_count = sum(1 for _, passed in results if passed)
    total_count = len(results)
    print(f"\nOverall: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 All tests passed! API is ready for your team.")
    else:
        print("⚠️ Some tests failed. Check the API setup.")

if __name__ == "__main__":
    main()
