# Petrol Pump AI API Tester

A simple HTML/JavaScript web application to test your Petrol Pump AI API endpoints for ANPR (License Plate Recognition) and Dispenser Reading.

## Features

- ✅ **Connection Testing**: Test if your API is running and accessible
- 🚙 **ANPR Testing**: Upload images or provide URLs to test license plate recognition
- ⛽ **Dispenser Testing**: Upload images or provide URLs to test dispenser reading
- 📱 **Mobile-Friendly**: Responsive design that works on desktop and mobile
- 🎨 **Beautiful UI**: Modern, clean interface with real-time feedback

## How to Use

### 1. Start Your API
Make sure your Petrol Pump AI API is running. If using Docker:
```bash
docker run -d -p 8000:8000 --name petrol-pump-test petrol-pump-ai
```

### 2. Open the Tester
Simply double-click on `index.html` to open it in your web browser.

### 3. Configure API URL
- The default URL is `http://localhost:8000`
- If your API is running on a different port or server, update the URL
- Click "Test Connection" to verify the API is accessible

### 4. Test ANPR (License Plate Recognition)
- **Option 1**: Click "Choose File" and select an image from your computer
- **Option 2**: Enter an image URL in the "Image URL" field
- Click "🔍 Analyze License Plate" to test the `/anpr/analyze` endpoint
- Results will show the detected license plate information

### 5. Test Dispenser Reading
- **Option 1**: Click "Choose File" and select an image from your computer
- **Option 2**: Enter an image URL in the "Image URL" field
- Click "📊 Read Dispenser" to test the `/dispenser/analyze` endpoint
- Results will show the detected dispenser readings

## API Endpoints Tested

### Health Check
- **Endpoint**: `GET /health`
- **Purpose**: Verify API is running
- **Response**: `{"status":"healthy","service":"Petrol Pump AI API"}`

### ANPR Analysis
- **Endpoint**: `POST /anpr/analyze`
- **Purpose**: Analyze license plates in images
- **Input**: Image file (multipart/form-data)
- **Response**: JSON with detected license plate information

### Dispenser Analysis
- **Endpoint**: `POST /dispenser/analyze`
- **Purpose**: Read dispenser displays in images
- **Input**: Image file (multipart/form-data)
- **Response**: JSON with detected dispenser readings

## Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)
- BMP (.bmp)

## Testing Tips

### For React Native App Development
1. **Test with Mobile Images**: Use your phone to take pictures and test them
2. **Test Different Conditions**: Try various lighting, angles, and image qualities
3. **Check Response Format**: Ensure the JSON response matches what your app expects
4. **Test Error Handling**: Try invalid images to see error responses

### Sample Test Images
You can use these sample image URLs for testing:

**License Plates:**
- `https://example.com/license-plate-1.jpg`
- `https://example.com/license-plate-2.jpg`

**Dispensers:**
- `https://example.com/dispenser-1.jpg`
- `https://example.com/dispenser-2.jpg`

### Performance Testing
- **First Request**: May take longer as models load (lazy loading)
- **Subsequent Requests**: Should be faster as models are cached
- **Monitor**: Check browser developer tools for request timing

## Troubleshooting

### Connection Issues
- ✅ Verify API is running: `curl http://localhost:8000/health`
- ✅ Check firewall settings
- ✅ Ensure correct port (default: 8000)

### CORS Issues
If testing from a different domain, you may need to enable CORS in your API.

### Image Upload Issues
- ✅ Check image file size (should be reasonable)
- ✅ Verify image format is supported
- ✅ Test with different images

### Model Loading Issues
- ✅ First request may take 30-60 seconds as models load
- ✅ Check Docker container logs: `docker logs petrol-pump-test`
- ✅ Ensure sufficient memory is available

## Browser Compatibility

- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari
- ✅ Edge

## Files Structure

```
petrol-pump-api-tester/
├── index.html          # Main HTML file
├── script.js           # JavaScript functionality
└── README.md          # This file
```

## Next Steps

Once you've tested your API with this tool:

1. **Document API Responses**: Note the exact JSON structure for your React Native app
2. **Test Edge Cases**: Try blurry, dark, or angled images
3. **Performance Benchmarks**: Record typical response times
4. **Error Handling**: Document all possible error responses
5. **Security**: Plan for authentication if needed in production

## Support

If you encounter issues:
1. Check the browser console for JavaScript errors
2. Verify your API is running and accessible
3. Test the API directly with curl or Postman
4. Check Docker container logs if using Docker

Happy testing! 🚗⛽
