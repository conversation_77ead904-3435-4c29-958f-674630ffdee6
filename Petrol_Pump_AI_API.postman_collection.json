{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "Petrol Pump AI API", "description": "Complete API collection for testing Petrol Pump AI endpoints including ANPR and Dispenser reading with file uploads and URL support", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has status field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData.status).to.eql('healthy');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check if the API is running and healthy"}, "response": []}, {"name": "ANPR - File Upload", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has plate_text field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('plate_text');", "    pm.expect(jsonData).to.have.property('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Upload an image file containing a license plate"}]}, "url": {"raw": "{{base_url}}/anpr", "host": ["{{base_url}}"], "path": ["anpr"]}, "description": "Analyze license plate from uploaded image file. Supports JPG, PNG, GIF, WebP, BMP formats."}, "response": []}, {"name": "ANPR - Image URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has plate_text field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('plate_text');", "    pm.expect(jsonData).to.have.property('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image_url", "value": "https://example.com/license-plate.jpg", "description": "URL of image containing license plate", "type": "text"}]}, "url": {"raw": "{{base_url}}/anpr", "host": ["{{base_url}}"], "path": ["anpr"]}, "description": "Analyze license plate from image URL. The API will download and process the image."}, "response": []}, {"name": "Dispenser - File Upload", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has amount field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('amount');", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('price_per_litre');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Upload an image file containing a dispenser display"}]}, "url": {"raw": "{{base_url}}/dispenser", "host": ["{{base_url}}"], "path": ["dispenser"]}, "description": "Read dispenser display from uploaded image file. Supports JPG, PNG, GIF, WebP, BMP formats."}, "response": []}, {"name": "Dispenser - Image URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has amount field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('amount');", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('price_per_litre');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image_url", "value": "https://example.com/dispenser.jpg", "description": "URL of image containing dispenser display", "type": "text"}]}, "url": {"raw": "{{base_url}}/dispenser", "host": ["{{base_url}}"], "path": ["dispenser"]}, "description": "Read dispenser display from image URL. The API will download and process the image."}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string", "description": "Base URL for the Petrol Pump AI API"}]}