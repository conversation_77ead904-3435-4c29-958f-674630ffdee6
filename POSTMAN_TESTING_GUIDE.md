# 📮 Postman Testing Guide for Petrol Pump AI API

## 🚀 Quick Start

### 1. Import Collection
1. Open Postman
2. Click **Import** button
3. Select `Petrol_Pump_AI_API.postman_collection.json`
4. Collection will appear in your workspace

### 2. Set Environment Variables
- **base_url**: `http://localhost:8000` (default)
- Change if your API runs on different port/server

### 3. Test API Health
Run the **Health Check** request first to verify API connectivity.

## 📋 Available Endpoints

### 🔍 Health Check
- **Method**: GET
- **Endpoint**: `/health`
- **Purpose**: Verify API is running
- **Expected Response**:
```json
{
  "status": "healthy",
  "service": "Petrol Pump AI API"
}
```

### 🚗 ANPR (License Plate Recognition)

#### File Upload Method
- **Method**: POST
- **Endpoint**: `/anpr`
- **Body**: Form-data with `file` field
- **Supported Formats**: JPG, PNG, GIF, WebP, BMP
- **Expected Response**:
```json
{
  "success": true,
  "plate_text": "TN 43 J0159",
  "message": "License plate analysis completed successfully"
}
```

#### URL Method
- **Method**: POST
- **Endpoint**: `/anpr`
- **Body**: Form-data with `image_url` field
- **Example URL**: `https://example.com/license-plate.jpg`

### ⛽ Dispenser Reading

#### File Upload Method
- **Method**: POST
- **Endpoint**: `/dispenser`
- **Body**: Form-data with `file` field
- **Supported Formats**: JPG, PNG, GIF, WebP, BMP
- **Expected Response**:
```json
{
  "success": true,
  "amount": "1250.50",
  "amount_numeric": 1250.50,
  "price_per_litre": 105.50,
  "calculated_litre": 11.85,
  "message": "Dispenser reading completed successfully"
}
```

#### URL Method
- **Method**: POST
- **Endpoint**: `/dispenser`
- **Body**: Form-data with `image_url` field
- **Example URL**: `https://example.com/dispenser.jpg`

## 🧪 Testing Tips

### For File Uploads
1. Click on **Body** tab
2. Select **form-data**
3. Set key as `file`
4. Change type to **File**
5. Click **Select Files** and choose your image

### For URL Testing
1. Click on **Body** tab
2. Select **form-data**
3. Set key as `image_url`
4. Set value to your image URL
5. Ensure URL is publicly accessible

### Performance Testing
- **First Request**: May take 30-60 seconds (models loading)
- **Subsequent Requests**: Should be fast (< 5 seconds)
- **Monitor**: Check response times in Postman

## 🔧 Troubleshooting

### Common Issues

#### 503 Service Unavailable
- **Cause**: Models still loading
- **Solution**: Wait 1-2 minutes and retry

#### 400 Bad Request
- **Cause**: Invalid image or missing parameters
- **Solution**: Check image format and request body

#### 500 Internal Server Error
- **Cause**: Server error during processing
- **Solution**: Check API logs and image quality

### Error Response Format
```json
{
  "detail": "Error message describing the issue"
}
```

## 📊 Test Scenarios

### Basic Functionality
1. ✅ Health check passes
2. ✅ ANPR with file upload works
3. ✅ ANPR with URL works
4. ✅ Dispenser with file upload works
5. ✅ Dispenser with URL works

### Error Handling
1. ❌ Invalid image format
2. ❌ Broken image URL
3. ❌ Missing parameters
4. ❌ Large file upload

### Performance
1. ⏱️ First request timing
2. ⏱️ Subsequent request timing
3. 📈 Concurrent requests

## 🎯 Production Testing

### Image Quality Testing
- Test with different lighting conditions
- Test with various angles
- Test with different resolutions
- Test with mobile camera images

### Load Testing
- Use Postman Runner for multiple requests
- Test concurrent API calls
- Monitor response times under load

## 📝 Notes

- **File Size Limit**: Recommended < 10MB per image
- **Timeout**: Requests may timeout after 30 seconds
- **Rate Limiting**: No current limits (add in production)
- **Authentication**: None required (add in production)

## 🔗 Related Files

- `Petrol_Pump_AI_API.postman_collection.json` - Main collection
- `petrol-pump-api-tester/` - Web-based tester
- `README.md` - Main project documentation
