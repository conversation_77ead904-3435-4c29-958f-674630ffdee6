# app/anpr_reader.py

import os
import cv2
import torch
import numpy as np
from PIL import Image
from ultralytics import YOLO
from transformers import TrOCRProcessor, VisionEncoderDecoderModel

# Global variables to hold models (loaded lazily)
yolo_model = None
processor = None
trocr_model = None

def load_models():
    """Load models with optimized settings"""
    global yolo_model, processor, trocr_model

    if yolo_model is None:
        print("Loading YOLO model...")
        # Handle PyTorch 2.6+ security settings for YOLO model loading
        try:
            yolo_model = YOLO("models/license_plate_detector.pt")
        except Exception as e:
            print(f"Warning: Failed to load YOLO model with default settings: {e}")
            # Try loading with weights_only=False by patching torch.load temporarily
            import torch
            original_load = torch.load
            torch.load = lambda *args, **kwargs: original_load(*args, **kwargs, weights_only=False)
            try:
                yolo_model = YOLO("models/license_plate_detector.pt")
            finally:
                torch.load = original_load
        print("YOLO model loaded successfully!")

    if processor is None:
        print("Loading TrOCR processor...")
        try:
            processor = TrOCRProcessor.from_pretrained("models/models/trocr")
            print("TrOCR processor loaded successfully!")
        except Exception as e:
            print(f"Error loading TrOCR processor: {e}")
            raise

    if trocr_model is None:
        print("Loading TrOCR model...")
        try:
            # Load with basic settings for CPU
            trocr_model = VisionEncoderDecoderModel.from_pretrained(
                "models/models/trocr",
                torch_dtype=torch.float32  # Use float32 for CPU compatibility
            )
            trocr_model.to("cpu")
            trocr_model.eval()  # Set to evaluation mode
            print("TrOCR model loaded successfully!")
        except Exception as e:
            print(f"Error loading TrOCR model: {e}")
            raise

def enhance_plate_image(crop):
    gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
    clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
    enhanced = clahe.apply(gray)
    filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
    return cv2.cvtColor(filtered, cv2.COLOR_GRAY2BGR)

def is_single_line_plate(crop):
    h, w = crop.shape[:2]
    aspect_ratio = w / h
    if aspect_ratio > 3.0:
        return True
    if aspect_ratio < 2.1:
        return False

    gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    horizontal_projection = np.sum(binary, axis=1)
    if np.max(horizontal_projection) == 0:
        return True
    normalized = horizontal_projection / np.max(horizontal_projection)
    text_rows = normalized > 0.3

    regions = []
    in_region = False
    start = 0
    for i, has_text in enumerate(text_rows):
        if has_text and not in_region:
            start = i
            in_region = True
        elif not has_text and in_region:
            if i - start > h * 0.1:
                regions.append((start, i))
            in_region = False
    if in_region and len(text_rows) - start > h * 0.1:
        regions.append((start, len(text_rows)))

    if len(regions) <= 1:
        return True
    elif len(regions) == 2 and regions[1][0] - regions[0][1] > h * 0.1:
        return False
    return True

def detect_and_read_plate(image_path):
    # Models are pre-loaded at startup, no need to load again

    if not os.path.exists(image_path):
        return "Image not found."

    img = cv2.imread(image_path)
    img_resized = cv2.resize(img, (640, 640))
    results = yolo_model(img_resized)

    if not results or not results[0].boxes:
        return "No license plate detected."

    best_result = ""
    best_conf = 0

    for box in results[0].boxes:
        x1, y1, x2, y2 = map(int, box.xyxy[0])
        conf = float(box.conf[0])
        crop = img_resized[y1:y2, x1:x2]
        if crop.size == 0:
            continue

        enhanced = enhance_plate_image(crop)
        if is_single_line_plate(enhanced):
            aligned = enhanced
        else:
            h = enhanced.shape[0]
            top = enhanced[:h//2, :]
            bottom = enhanced[h//2:, :]
            height = 64
            top_r = cv2.resize(top, (int(top.shape[1] * height / top.shape[0]), height)) if top.size else np.zeros((height, 100, 3), dtype=np.uint8)
            bottom_r = cv2.resize(bottom, (int(bottom.shape[1] * height / bottom.shape[0]), height)) if bottom.size else np.zeros((height, 100, 3), dtype=np.uint8)
            aligned = cv2.hconcat([top_r, bottom_r])

        pil_image = Image.fromarray(cv2.cvtColor(aligned, cv2.COLOR_BGR2RGB))
        pixel_values = processor(images=pil_image, return_tensors="pt").pixel_values

        with torch.no_grad():
            generated_ids = trocr_model.generate(pixel_values)
            text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0].strip()

        if conf > best_conf and len(text) > 3:
            best_conf = conf
            best_result = text

    return best_result if best_result else "No valid plates detected."
