# Base image
FROM python:3.10-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Configure pip for better timeout handling
ENV PIP_DEFAULT_TIMEOUT=1000
ENV PIP_RETRIES=5
ENV PIP_TIMEOUT=1000

# Install basic dependencies
RUN apt-get update && apt-get install -y \
    git \
    build-essential \
    tesseract-ocr \
    libgl1 \
    libglib2.0-0 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip to latest version
RUN pip install --upgrade pip

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies with extended timeout
COPY requirements.txt .
RUN pip install --no-cache-dir --timeout=1000 --retries=5 -r requirements.txt

# Copy rest of the application
COPY . .

# Expose the port FastAPI runs on
EXPOSE 8000

# Run the FastAPI app using uvicorn
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
