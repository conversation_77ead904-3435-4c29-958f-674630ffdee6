# app/dispenser_reader.py

import cv2
import numpy as np
from PIL import Image
import pytesseract
from transformers import TrOCRProcessor, VisionEncoderDecoderModel
import easyocr
import torch
import re
from collections import Counter
import os
import datetime

# Global variables to hold models (loaded lazily)
processor = None
trocr_model = None
easyocr_reader = None

def load_dispenser_models():
    """Load models with optimized settings"""
    global processor, trocr_model, easyocr_reader

    if processor is None:
        print("Loading TrOCR processor for dispenser...")
        try:
            processor = TrOCRProcessor.from_pretrained("microsoft/trocr-large-printed")
            print("TrOCR processor loaded successfully!")
        except Exception as e:
            print(f"Error loading TrOCR processor: {e}")
            raise

    if trocr_model is None:
        print("Loading TrOCR model for dispenser...")
        try:
            # Load with optimized settings for CPU
            trocr_model = VisionEncoderDecoderModel.from_pretrained(
                "microsoft/trocr-large-printed",
                torch_dtype=torch.float32,  # Use float32 for CPU compatibility
                low_cpu_mem_usage=True      # Optimize memory usage
            )

            # Set to CPU explicitly (since we're using CPU-only Docker)
            device = "cpu"
            trocr_model.to(device)
            trocr_model.eval()  # Set to evaluation mode

            print(f"TrOCR model loaded successfully on {device}!")
        except Exception as e:
            print(f"Error loading TrOCR model: {e}")
            raise

    if easyocr_reader is None:
        print("Loading EasyOCR reader...")
        try:
            # Load EasyOCR with CPU-only settings
            easyocr_reader = easyocr.Reader(['en'], gpu=False)
            print("EasyOCR reader loaded successfully!")
        except Exception as e:
            print(f"Error loading EasyOCR: {e}")
            raise

def format_decimal_result(text):
    if not isinstance(text, str):
        return ""
    clean_text = re.sub(r'[^0-9.]', '', text)
    if not clean_text:
        return ""
    if '.' in clean_text:
        parts = clean_text.split('.')
        return parts[0] + '.' + ''.join(parts[1:]) if len(parts) > 2 else clean_text
    if len(clean_text) >= 3:
        return clean_text[:-2] + '.' + clean_text[-2:]
    elif len(clean_text) == 2:
        return '0.' + clean_text
    elif len(clean_text) == 1:
        return '0.0' + clean_text
    return clean_text

def trocr_ocr(image_array):
    load_dispenser_models()  # Lazy load models

    if image_array is None or image_array.size == 0:
        return ""
    if len(image_array.shape) == 3:
        pil_image = Image.fromarray(cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB))
    else:
        pil_image = Image.fromarray(image_array).convert('RGB')
    pixel_values = processor(images=pil_image, return_tensors="pt").pixel_values.to(trocr_model.device)
    with torch.no_grad():
        generated_ids = trocr_model.generate(pixel_values, max_length=50)
    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
    return generated_text.strip()

def evaluate_best_result(results, trocr_specific_results):
    valid_trocr_results = []
    for trocr_res in trocr_specific_results:
        formatted = format_decimal_result(trocr_res)
        if re.search(r'\d', formatted) and formatted.count('.') <= 1:
            try:
                val = float(formatted)
                if 0 < val < 100000:
                    valid_trocr_results.append(formatted)
            except ValueError:
                pass
    if valid_trocr_results:
        counts = Counter(valid_trocr_results)
        return counts.most_common(1)[0][0]

    valid_numeric_results = []
    for res in results:
        formatted = format_decimal_result(res)
        if re.search(r'\d', formatted) and formatted.count('.') <= 1:
            try:
                val = float(formatted)
                if 0 < val < 100000:
                    valid_numeric_results.append(formatted)
            except ValueError:
                pass
    if not valid_numeric_results:
        return "No numeric results"

    counts = Counter(valid_numeric_results)
    return counts.most_common(1)[0][0]

def perform_full_ocr_analysis(image, region_label):
    # Lazy load models
    load_dispenser_models()

    if image is None or image.size == 0:
        return "N/A"

    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    inverted = cv2.bitwise_not(gray)
    blurred = cv2.GaussianBlur(inverted, (5, 5), 0)
    _, thresh1 = cv2.threshold(blurred, 127, 255, cv2.THRESH_BINARY)
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
    cleaned = cv2.morphologyEx(thresh1, cv2.MORPH_CLOSE, kernel)

    config = '--psm 8 -c tessedit_char_whitelist=0123456789.'
    text1_tesseract = pytesseract.image_to_string(cleaned, config=config).strip()
    text1_trocr = trocr_ocr(cleaned)

    blur2 = cv2.GaussianBlur(gray, (3, 3), 0)
    _, thresh2 = cv2.threshold(blur2, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    contours, _ = cv2.findContours(thresh2, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    digits = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        if (h > 15 and w > 5 and 0.1 <= (w / float(h)) <= 2.0 and cv2.contourArea(contour) > 50):
            roi = thresh2[y:y+h, x:x+w]
            roi = cv2.bitwise_not(roi)
            scale_factor = max(2, 64 // max(w, h, 1))
            roi_resized = cv2.resize(roi, (w * scale_factor, h * scale_factor), interpolation=cv2.INTER_CUBIC)
            roi_padded = cv2.copyMakeBorder(roi_resized, 10, 10, 10, 10, cv2.BORDER_CONSTANT, value=0)
            digit_tesseract = pytesseract.image_to_string(roi_padded, config='--psm 10 -c tessedit_char_whitelist=0123456789').strip()
            digit_trocr = trocr_ocr(roi_padded)
            digits.append(digit_tesseract if digit_tesseract.isdigit() else digit_trocr)

    text2_contours = ''.join(digits)
    text5_easyocr = ' '.join([res[1] for res in easyocr_reader.readtext(cleaned, allowlist='0123456789.')])

    results = [text1_tesseract, text2_contours, text5_easyocr]
    trocr_results = [text1_trocr]
    return evaluate_best_result(results, trocr_results)

PRICE_FILE_PATH = "price_per_litre.txt"
LAST_RUN_DATE_FILE = "last_run_date.txt"

def get_or_set_price_per_litre():
    today = datetime.date.today()
    last_run_date = None
    if os.path.exists(LAST_RUN_DATE_FILE):
        try:
            with open(LAST_RUN_DATE_FILE, 'r') as f:
                date_str = f.read().strip()
                last_run_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
        except:
            pass

    if last_run_date == today and os.path.exists(PRICE_FILE_PATH):
        try:
            with open(PRICE_FILE_PATH, 'r') as f:
                price_str = f.read().strip()
                current_price = float(price_str)
                if current_price > 0:
                    return current_price
        except:
            pass

    while True:
        try:
            user_input = input("Enter current price per litre (e.g., 105.50): ").strip()
            price = float(user_input)
            if price > 0:
                with open(PRICE_FILE_PATH, 'w') as f:
                    f.write(str(price))
                with open(LAST_RUN_DATE_FILE, 'w') as f:
                    f.write(str(today))
                return price
        except:
            print("Invalid input. Please enter a valid number.")
