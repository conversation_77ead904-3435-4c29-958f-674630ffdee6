# app/dispenser_reader.py

import os
import re
import cv2
import torch
import datetime
from PIL import Image
from collections import Counter
from transformers import TrOCRProcessor, VisionEncoderDecoderModel
from ultralytics import YOLO
import numpy as np
from typing import Dict, Any

# Global variables for models
processor = None
trocr_model = None
yolo_model = None
device = None

# Price management files (persistent storage)
PRICE_DATA_DIR = "price_data"
PRICE_FILE = os.path.join(PRICE_DATA_DIR, "price_per_litre.txt")
DATE_FILE = os.path.join(PRICE_DATA_DIR, "last_run_date.txt")

# Ensure price data directory exists
os.makedirs(PRICE_DATA_DIR, exist_ok=True)

def load_dispenser_models():
    """Load models with optimized settings"""
    global processor, trocr_model, yolo_model, device

    print("\n🚀 Initializing dispenser models...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Load YOLO model
    if yolo_model is None:
        print("Loading YOLO model...")
        try:
            # You'll need to update this path to your YOLO model
            yolo_model = YOLO("models/best.pt")  # Update path as needed
            print("✅ YOLOv8 loaded")
        except Exception as e:
            print(f"Error loading YOLO model: {e}")
            raise

    # Load TrOCR processor from local path
    if processor is None:
        print("Loading TrOCR processor...")
        try:
            processor = TrOCRProcessor.from_pretrained('models/models/trocr')
            print("✅ TrOCR processor loaded")
        except Exception as e:
            print(f"Error loading TrOCR processor: {e}")
            raise

    # Load TrOCR model from local path
    if trocr_model is None:
        print("Loading TrOCR model...")
        try:
            trocr_model = VisionEncoderDecoderModel.from_pretrained('models/models/trocr')
            trocr_model.to(device)
            trocr_model.eval()
            print(f"✅ TrOCR loaded on {device}")
        except Exception as e:
            print(f"Error loading TrOCR model: {e}")
            raise

def format_decimal_result(text):
    """Format text to decimal result with improved logic"""
    if not isinstance(text, str):
        return ""
    clean = re.sub(r'[^0-9.]', '', text)
    if not clean:
        return ""
    if '.' in clean:
        parts = clean.split('.')
        return parts[0] + '.' + ''.join(parts[1:]) if len(parts) > 2 else clean
    if len(clean) >= 3:
        return clean[:-2] + '.' + clean[-2:]
    elif len(clean) == 2:
        return '0.' + clean
    elif len(clean) == 1:
        return '0.0' + clean
    return clean

def trocr_ocr(image_array):
    """Perform TrOCR on image array"""
    load_dispenser_models()  # Ensure models are loaded

    if image_array is None or image_array.size == 0:
        return ""
    pil_image = Image.fromarray(cv2.cvtColor(image_array, cv2.COLOR_BGR2RGB))
    pixel_values = processor(images=pil_image, return_tensors="pt").pixel_values.to(device)
    with torch.no_grad():
        ids = trocr_model.generate(pixel_values, max_length=50)
    return processor.batch_decode(ids, skip_special_tokens=True)[0].strip()

def evaluate_best_result(results):
    """Evaluate best result from OCR results"""
    print("\n--- Evaluating best result ---")
    valid = []
    for r in results:
        f = format_decimal_result(r)
        try:
            val = float(f)
            if 0 < val < 100000:
                valid.append(f)
        except:
            pass
    if not valid:
        return "No valid results"
    return Counter(valid).most_common(1)[0][0]

def perform_ocr(image_crop):
    """Perform OCR on cropped region - simplified and optimized"""
    print("\n🔍 OCR on cropped region...")

    # Enhanced processing
    gray = cv2.cvtColor(image_crop, cv2.COLOR_BGR2GRAY)
    _, th = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    proc = cv2.morphologyEx(th, cv2.MORPH_CLOSE, kernel)
    proc = cv2.morphologyEx(proc, cv2.MORPH_OPEN, kernel)
    scaled = cv2.resize(cv2.bitwise_not(proc), None, fx=3, fy=3, interpolation=cv2.INTER_CUBIC)

    # Only use TrOCR methods for best accuracy
    trocr_res_1 = trocr_ocr(scaled)
    trocr_res_2 = trocr_ocr(image_crop)
    formatted = evaluate_best_result([trocr_res_1, trocr_res_2])

    print("\n🏆 Best TrOCR Result:", formatted)
    return formatted

def get_price():
    """Get today's price per litre"""
    today = datetime.date.today()
    try:
        with open(DATE_FILE) as f:
            last = datetime.datetime.strptime(f.read().strip(), '%Y-%m-%d').date()
    except:
        last = None

    if last == today and os.path.exists(PRICE_FILE):
        try:
            val = float(open(PRICE_FILE).read().strip())
            if val > 0:
                return val
        except:
            pass

    # For API usage, return a default price if not set
    # In production, this should be set via admin interface
    default_price = 105.50  # Default price per litre
    print(f"Using default price: ₹{default_price}/litre")
    return default_price

def set_price(price: float):
    """Set today's price per litre"""
    today = datetime.date.today()
    if price > 0:
        with open(PRICE_FILE, 'w') as f:
            f.write(str(price))
        with open(DATE_FILE, 'w') as f:
            f.write(str(today))
        return True
    return False

def analyze_dispenser_display(image_path: str) -> Dict[str, Any]:
    """Main function to analyze dispenser display and calculate litres"""
    # Models are pre-loaded at startup, no need to load again

    print("\n📸 Loading image...")
    img = cv2.imread(image_path)
    if img is None:
        raise FileNotFoundError(f"Image not found: {image_path}")

    print("\n🔍 Detecting regions with YOLO...")
    results = yolo_model(image_path, verbose=False)
    boxes = [b for r in results for b in r.boxes]

    CLASS_MAP = {i: name.lower() for i, name in enumerate(['-dispenser', '22', 'Amount', 'Dispenser', 'Display', 'Litre'])}
    TARGET_CLASSES = ['amount']

    amount = "Not Detected"

    for box in boxes:
        cls_id = int(box.cls[0])
        label = CLASS_MAP.get(cls_id, '')
        if label not in TARGET_CLASSES:
            continue
        conf = float(box.conf[0])
        print(f" - Found {label.upper()} with {conf:.2f} confidence")
        x1, y1, x2, y2 = map(int, box.xyxy[0])
        pad_top = int(0.28 * (y2 - y1))
        pad_bottom = int(0.10 * (y2 - y1))
        crop = img[y1 + pad_top:y2 - pad_bottom, x1:x2]
        amount = perform_ocr(crop)
        break

    print("\n💰 Final Amount:", amount)

    # Get price and calculate litres
    price = get_price()
    litres = "Could not calculate"

    try:
        val = float(amount)
        litres = val / price
        print(f"⛽ Litres (@ ₹{price:.2f}/litre): {litres:.2f}")
    except:
        print("Could not calculate litres due to invalid amount.")

    return {
        "amount": amount,
        "price_per_litre": price,
        "litres": litres if isinstance(litres, float) else None,
        "success": amount != "Not Detected"
    }
