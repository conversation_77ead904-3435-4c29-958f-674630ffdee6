#!/bin/bash

# Production deployment script for Petrol Pump AI API
echo "🚀 Starting Petrol Pump AI API deployment..."

# Stop and remove existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Remove old images to ensure fresh build
echo "🧹 Cleaning up old images..."
docker image prune -f

# Build and start the application
echo "🔨 Building and starting the application..."
docker-compose up --build -d

# Wait for the application to start
echo "⏳ Waiting for application to start..."
sleep 30

# Check if the application is healthy
echo "🔍 Checking application health..."
for i in {1..10}; do
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ Application is healthy and ready!"
        echo "🌐 API is available at: http://localhost:8000"
        echo "📋 Health check: http://localhost:8000/health"
        echo "📖 API docs: http://localhost:8000/docs"
        break
    else
        echo "⏳ Waiting for application to be ready... (attempt $i/10)"
        sleep 10
    fi
done

# Show container status
echo "📊 Container status:"
docker-compose ps

# Show logs
echo "📝 Recent logs:"
docker-compose logs --tail=20 petrol-pump-api

echo "🎉 Deployment complete!"
echo ""
echo "📋 Quick commands:"
echo "  View logs: docker-compose logs -f petrol-pump-api"
echo "  Stop API:  docker-compose down"
echo "  Restart:   docker-compose restart"
echo "  Update:    ./deploy.sh"
