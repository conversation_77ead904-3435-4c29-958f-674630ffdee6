# app/utils.py

import os
import cv2
import numpy as np
from fastapi import UploadFile
from datetime import datetime
from typing import Union

def save_uploaded_file(uploaded_file: UploadFile, upload_dir: str = "uploads") -> str:
    """
    Save uploaded image file to a directory and return the full file path.
    """
    os.makedirs(upload_dir, exist_ok=True)
    file_ext = uploaded_file.filename.split('.')[-1]
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"{timestamp}_{uploaded_file.filename}"
    file_path = os.path.join(upload_dir, filename)

    with open(file_path, "wb") as f:
        f.write(uploaded_file.file.read())

    return file_path

def read_image(file_path: str) -> Union[np.ndarray, None]:
    """
    Read image using OpenCV from the given file path.
    """
    if not os.path.exists(file_path):
        return None
    return cv2.imread(file_path)

def clean_plate_text(text: str) -> str:
    """
    Clean and format the extracted license plate text.
    """
    import re
    clean = re.sub(r'[^A-Z0-9]', '', text.upper())
    return clean
