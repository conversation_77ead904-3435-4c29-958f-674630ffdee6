{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "Petrol Pump AI API - Optimized", "description": "Complete API collection for Petrol Pump AI with optimized dispenser analysis and daily price management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "Set Daily Price", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/set-price?price=105.50", "host": ["{{base_url}}"], "path": ["set-price"], "query": [{"key": "price", "value": "105.50", "description": "Price per litre (e.g., 105.50)"}]}}, "response": []}, {"name": "Get Current Price", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/get-price", "host": ["{{base_url}}"], "path": ["get-price"]}}, "response": []}, {"name": "Dispenser - File Upload", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Upload dispenser display image"}]}, "url": {"raw": "{{base_url}}/dispenser", "host": ["{{base_url}}"], "path": ["dispenser"]}}, "response": []}, {"name": "Dispenser - Image URL", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image_url", "value": "https://example.com/dispenser-image.jpg", "type": "text", "description": "URL of dispenser display image"}]}, "url": {"raw": "{{base_url}}/dispenser", "host": ["{{base_url}}"], "path": ["dispenser"]}}, "response": []}, {"name": "ANPR - File Upload", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Upload license plate image"}]}, "url": {"raw": "{{base_url}}/anpr", "host": ["{{base_url}}"], "path": ["anpr"]}}, "response": []}, {"name": "ANPR - Image URL", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "image_url", "value": "https://example.com/license-plate.jpg", "type": "text", "description": "URL of license plate image"}]}, "url": {"raw": "{{base_url}}/anpr", "host": ["{{base_url}}"], "path": ["anpr"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}]}